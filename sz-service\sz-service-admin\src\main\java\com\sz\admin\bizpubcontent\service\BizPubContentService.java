package com.sz.admin.bizpubcontent.service;

import com.mybatisflex.core.service.IService;
import com.sz.admin.bizpubcontent.pojo.po.BizPubContent;
import com.sz.core.common.entity.SelectIdsDTO;
import com.sz.core.common.entity.PageResult;

import java.sql.SQLException;
import java.util.List;
import com.sz.admin.bizpubcontent.pojo.dto.BizPubContentCreateDTO;
import com.sz.admin.bizpubcontent.pojo.dto.BizPubContentUpdateDTO;
import com.sz.admin.bizpubcontent.pojo.dto.BizPubContentListDTO;
import com.sz.admin.bizpubcontent.pojo.vo.BizPubContentVO;
import com.sz.core.common.entity.ImportExcelDTO;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <p>
 * 内容发布表 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
public interface BizPubContentService extends IService<BizPubContent> {

    void create(BizPubContentCreateDTO dto);

    void update(BizPubContentUpdateDTO dto);

    void updateByAction(BizPubContentUpdateDTO dto);

    PageResult<BizPubContentVO> page(BizPubContentListDTO dto) throws SQLException;

    PageResult<BizPubContentVO> reviewPage(BizPubContentListDTO dto) throws SQLException;

    List<BizPubContentVO> list(BizPubContentListDTO dto);

    void remove(SelectIdsDTO dto);

    BizPubContentVO detail(Object id) throws SQLException;

    void importExcel(ImportExcelDTO dto);

    void exportExcel(BizPubContentListDTO dto, HttpServletResponse response);
}