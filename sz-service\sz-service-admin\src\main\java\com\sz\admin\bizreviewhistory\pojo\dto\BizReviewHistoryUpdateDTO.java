package com.sz.admin.bizreviewhistory.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * BizReviewHistory修改DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@Schema(description = "BizReviewHistory修改DTO")
public class BizReviewHistoryUpdateDTO {

    @Schema(description =  "主键唯一")
    private Long reviewHistoryId;

    @Schema(description =  "审核状态")
    private String reviewStatus;

    @Schema(description =  "审核人")
    private Long auditor;

    @Schema(description =  "审核内容")
    private String reviewContent;

}