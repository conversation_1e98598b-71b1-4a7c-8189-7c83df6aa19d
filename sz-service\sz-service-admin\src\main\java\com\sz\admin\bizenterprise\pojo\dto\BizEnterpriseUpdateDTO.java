package com.sz.admin.bizenterprise.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * BizEnterprise修改DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@Schema(description = "BizEnterprise修改DTO")
public class BizEnterpriseUpdateDTO {

    @Schema(description =  "使用用户表ID")
    private Long enterpriseId;

    @Schema(description ="企业名称")
    private String enterpriseName;

    @Schema(description =  "统一社会信用代码")
    private String creditCode;

    @Schema(description =  "负责人手机号")
    private String leaderContact;

}