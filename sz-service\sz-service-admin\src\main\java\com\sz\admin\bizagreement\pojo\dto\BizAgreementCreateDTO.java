package com.sz.admin.bizagreement.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * BizAgreement添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@Schema(description = "BizAgreement添加DTO")
public class BizAgreementCreateDTO {

   @Schema(description =  "标题")
   private String title;

   @Schema(description =  "公示/协议 内容")
   private String agreementContent;

   @Schema(description =  "启用/禁用")
   private String isNot;

   @Schema(description =  "备注")
   private String remark;

   @Schema(description =  "排序")
   private Integer sortNum;

   @Schema(description =  "业务类型")
   private String agreementType;
}