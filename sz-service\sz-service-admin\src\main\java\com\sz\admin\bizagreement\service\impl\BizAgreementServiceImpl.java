package com.sz.admin.bizagreement.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.sz.admin.bizcommercial.service.BizCommercialService;
import com.sz.utils.QueryUtil;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.sz.admin.bizagreement.service.BizAgreementService;
import com.sz.admin.bizagreement.pojo.po.BizAgreement;
import com.sz.admin.bizagreement.mapper.BizAgreementMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.query.QueryChain;
import com.sz.core.common.enums.CommonResponseEnum;
import com.sz.core.util.PageUtils;
import com.sz.core.util.BeanCopyUtils;
import com.sz.core.util.Utils;
import com.sz.core.common.entity.PageResult;
import com.sz.core.common.entity.SelectIdsDTO;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import com.sz.admin.bizagreement.pojo.dto.BizAgreementCreateDTO;
import com.sz.admin.bizagreement.pojo.dto.BizAgreementUpdateDTO;
import com.sz.admin.bizagreement.pojo.dto.BizAgreementListDTO;
import com.sz.admin.bizagreement.pojo.dto.BizAgreementImportDTO;
import com.sz.core.common.entity.ImportExcelDTO;
import com.sz.excel.core.ExcelResult;
import java.io.OutputStream;
import jakarta.servlet.http.HttpServletResponse;
import com.sz.core.util.FileUtils;
import com.sz.excel.utils.ExcelUtils;
import lombok.SneakyThrows;
import com.sz.admin.bizagreement.pojo.vo.BizAgreementVO;

/**
 * <p>
 * 公示/协议内容表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Service
@RequiredArgsConstructor
public class BizAgreementServiceImpl extends ServiceImpl<BizAgreementMapper, BizAgreement> implements BizAgreementService {

    @Resource
    private BizCommercialService bizCommercialService;

    @Override
    public void create(BizAgreementCreateDTO dto){
        BizAgreement bizAgreement = BeanCopyUtils.copy(dto, BizAgreement.class);
        if (dto.getAgreementType().equals("2002002")) {
            //说明新增的这个是启用,其他的禁用
            if (dto.getIsNot().equals("2000001")) {
                QueryWrapper wrapper = QueryWrapper.create().eq(BizAgreement::getAgreementType, dto.getAgreementType());
                List<BizAgreement> list = list(wrapper);
                for (BizAgreement item : list) {
                    item.setIsNot("2000002");
                    updateById(item);
                }
                save(bizAgreement);
            } else {
                save(bizAgreement);
            }
        }else {
            save(bizAgreement);
        }
    }

    @Override
    public void update(BizAgreementUpdateDTO dto){
        BizAgreement bizAgreement = BeanCopyUtils.copy(dto, BizAgreement.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
            .eq(BizAgreement::getAgreementId, dto.getAgreementId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);
        // 如果 agreementType 为 2002002 则仅把当条改为启用,其他的此类型改为禁用
        if (dto.getAgreementType().equals("2002002")) {
            // 查询所有的 为 2002002 的列表
            wrapper = QueryWrapper.create().eq(BizAgreement::getAgreementType, dto.getAgreementType());
            List<BizAgreement> list = list(wrapper);
            for (BizAgreement item : list) {
                if (item.getAgreementId().equals(dto.getAgreementId())) {
                    item.setIsNot(dto.getIsNot());
                } else {
                    if (item.getIsNot().equals("2000002")) {
                        item.setIsNot("2000001");
                    }else {
                        item.setIsNot("2000002");
                    }
                }
            }
            // 批量更新
            updateBatch(list);
        }else {
            saveOrUpdate(bizAgreement);
        }
    }

    @Override
    public PageResult<BizAgreementVO> page(BizAgreementListDTO dto){
        Page<BizAgreementVO> page = pageAs(PageUtils.getPage(dto), buildQueryWrapper(dto), BizAgreementVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<BizAgreementVO> list(BizAgreementListDTO dto){
        return listAs(buildQueryWrapper(dto), BizAgreementVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto){
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public BizAgreementVO detail(Object id){
        BizAgreement bizAgreement = getById((Serializable) id);
        CommonResponseEnum.INVALID_ID.assertNull(bizAgreement);
        return BeanCopyUtils.copy(bizAgreement, BizAgreementVO.class);
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<BizAgreementImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), BizAgreementImportDTO.class, true);
        List<BizAgreementImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(BizAgreementListDTO dto, HttpServletResponse response) {
        List<BizAgreementVO> list = list(dto);
        String fileName = "公示/协议内容表模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "公示/协议内容表", BizAgreementVO.class, os);
    }

    @Override
    public JSONObject getMiniAppHome(JSONObject params) throws SQLException {
        //先查询广告图片列表
        QueryWrapper wrapper = QueryWrapper.create().select("sf.url").from("biz_commercial").as("bc")
            .leftJoin("sys_file").as("sf").on("bc.file_id = sf.id")
            .eq("bc.is_not", "2000001").orderBy("bc.sort_num asc");
        List<JSONObject> list = QueryUtil.list(wrapper,JSONObject.class);
        List<String> urls = new ArrayList<>();
        for (JSONObject item : list) {
            urls.add(item.getString("url"));
        }
        //查询公示列表信息
        QueryWrapper wrapper1 = QueryWrapper.create().select("ba.title,ba.agreement_id").from("biz_agreement").as("ba")
                .eq("ba.agreement_type", "2002001").eq("ba.is_not", "2000001").orderBy("ba.sort_num asc");
        List<JSONObject> list1 = QueryUtil.list(wrapper1,JSONObject.class);
        JSONObject result = new JSONObject();
        result.put("urls",urls);
        result.put("list",list1);
        return result;
    }

    private static QueryWrapper buildQueryWrapper(BizAgreementListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(BizAgreement.class);
        if (Utils.isNotNull(dto.getTitle())) {
            wrapper.like(BizAgreement::getTitle, dto.getTitle());
        }

        if (Utils.isNotNull(dto.getAgreementContent())) {
            wrapper.eq(BizAgreement::getAgreementContent, dto.getAgreementContent());
        }
        if (Utils.isNotNull(dto.getIsNot())) {
            wrapper.eq(BizAgreement::getIsNot, dto.getIsNot());
        }
        if (Utils.isNotNull(dto.getSortNum())) {
            wrapper.eq(BizAgreement::getSortNum, dto.getSortNum());
        }
        return wrapper;
    }
}