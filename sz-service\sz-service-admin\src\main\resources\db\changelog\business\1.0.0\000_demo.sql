--liquibase formatted sql

--changeset 升职哦（sz）:20250425_0924
-- 以下为演示环境脚本，-- 仅供演示使用，实际业务请删除
CREATE TABLE IF NOT EXISTS `teacher_statistics`  (
                                                     `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                                     `year` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '统计年限',
    `month` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '统计月份',
    `during_time` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统计年月',
    `teacher_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '教师id',
    `teacher_common_type` int NOT NULL COMMENT '讲师区分类型',
    `total_teaching` int NULL DEFAULT NULL COMMENT '授课总数',
    `total_class_count` int NULL DEFAULT NULL COMMENT '服务班次数',
    `total_hours` decimal(10, 2) NULL DEFAULT NULL COMMENT '课时总数',
    `check_status` int NOT NULL DEFAULT 0 COMMENT '核对状态',
    `check_time` datetime NULL DEFAULT NULL COMMENT '核对时间',
    `create_time` datetime NULL DEFAULT NULL COMMENT '生成时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    `last_sync_time` datetime NULL DEFAULT NULL COMMENT '最近一次同步时间',
    `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `create_id` bigint NULL DEFAULT NULL COMMENT '创建人ID',
    `update_id` bigint NULL DEFAULT NULL COMMENT '更新人ID',
    `dept_scope` json NULL COMMENT '部门范围',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教师统计总览表' ROW_FORMAT = DYNAMIC;

INSERT IGNORE INTO `teacher_statistics` (`id`, `year`, `month`, `during_time`, `teacher_id`, `teacher_common_type`, `total_teaching`, `total_class_count`, `total_hours`, `check_status`, `check_time`, `create_time`, `update_time`, `last_sync_time`, `remark`, `create_id`, `update_id`, `dept_scope`) VALUES (22, '2018', '12', '03', '1503', 1000001, 12, 22, 15.00, 1000001, '2024-07-08 10:39:56', '2024-07-08 10:40:16', NULL, '2024-07-08 10:39:57', 'test1 创建记录', 3, NULL, '[4]');
INSERT IGNORE INTO `teacher_statistics` (`id`, `year`, `month`, `during_time`, `teacher_id`, `teacher_common_type`, `total_teaching`, `total_class_count`, `total_hours`, `check_status`, `check_time`, `create_time`, `update_time`, `last_sync_time`, `remark`, `create_id`, `update_id`, `dept_scope`) VALUES (23, '2019', '12', '03', '111', 1000001, 1, 2, 3.00, 1000001, '2024-07-08 10:41:09', '2024-07-08 10:41:18', NULL, '2024-07-08 10:41:11', 'test1 创建记录', 3, NULL, '[4]');
INSERT IGNORE INTO `teacher_statistics` (`id`, `year`, `month`, `during_time`, `teacher_id`, `teacher_common_type`, `total_teaching`, `total_class_count`, `total_hours`, `check_status`, `check_time`, `create_time`, `update_time`, `last_sync_time`, `remark`, `create_id`, `update_id`, `dept_scope`) VALUES (24, '2020', '12', '2020-12', '023', 1000001, 1, 1, 1.00, 1000001, '2024-07-08 13:06:55', '2024-07-08 13:07:07', NULL, '2024-07-08 13:06:57', 'test1 创建记录', 3, NULL, '[4]');
INSERT IGNORE INTO `teacher_statistics` (`id`, `year`, `month`, `during_time`, `teacher_id`, `teacher_common_type`, `total_teaching`, `total_class_count`, `total_hours`, `check_status`, `check_time`, `create_time`, `update_time`, `last_sync_time`, `remark`, `create_id`, `update_id`, `dept_scope`) VALUES (25, '2021', '12', '2021-12', '123', 1000001, 1, 1, 1.00, 1000001, '2024-07-08 13:13:56', '2024-07-08 13:13:59', NULL, NULL, 'test2 创建记录', 4, NULL, '[15]');
INSERT IGNORE INTO `teacher_statistics` (`id`, `year`, `month`, `during_time`, `teacher_id`, `teacher_common_type`, `total_teaching`, `total_class_count`, `total_hours`, `check_status`, `check_time`, `create_time`, `update_time`, `last_sync_time`, `remark`, `create_id`, `update_id`, `dept_scope`) VALUES (26, '2022', '12', '2022-12', '13123', 1000001, 1, 1, 1.00, 1000001, '2024-07-08 13:15:36', '2024-07-08 13:15:37', '2024-07-08 13:15:46', NULL, 'test3 创建记录', 5, 5, '[15]');
INSERT IGNORE INTO `teacher_statistics` (`id`, `year`, `month`, `during_time`, `teacher_id`, `teacher_common_type`, `total_teaching`, `total_class_count`, `total_hours`, `check_status`, `check_time`, `create_time`, `update_time`, `last_sync_time`, `remark`, `create_id`, `update_id`, `dept_scope`) VALUES (27, '2099', '12', '12', '123123', 1000001, 1, 1, 1.00, 1000001, NULL, '2024-07-08 13:20:29', NULL, NULL, '管理员创建', 1, NULL, '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]');

INSERT IGNORE INTO `generator_table` (`table_id`, `table_name`, `table_comment`, `class_name`, `camel_class_name`, `tpl_category`, `package_name`, `module_name`, `business_name`, `function_name`, `function_author`, `type`, `options`, `parent_menu_id`, `path`, `path_api`, `path_web`, `menu_init_type`, `btn_permission_type`, `has_import`, `has_export`, `generate_type`, `create_id`, `update_id`, `create_time`, `update_time`, `is_autofill`) VALUES (1, 'teacher_statistics', '教师统计总览表', 'TeacherStatistics', 'teacherStatistics', 'crud', 'com.sz.admin', 'teacherstatistics', 'teacherStatistics', '教师统计总览表', 'sz-admin', '0', NULL, '0', '/', 'E:\\dev\\Code\\Github\\sz-boot-parent\\sz-service\\sz-service-admin', '', '1', '1', '1', '1', 'all', 1, NULL, '2024-05-10 21:45:32', NULL, '1');

INSERT IGNORE INTO `generator_table_column` (`column_id`, `table_id`, `column_name`, `column_comment`, `column_type`, `java_type`, `search_type`, `ts_type`, `java_type_package`, `java_field`, `up_camel_field`, `is_pk`, `is_increment`, `is_required`, `is_insert`, `is_edit`, `is_list`, `is_query`, `is_import`, `is_export`, `is_autofill`, `is_unique_valid`, `autofill_type`, `query_type`, `html_type`, `dict_type`, `is_logic_del`, `options`, `sort`, `create_id`, `update_id`, `create_time`, `update_time`, `dict_show_way`) VALUES (1, 1, 'id', 'id', 'int', 'Long', 'input', 'number', NULL, 'id', 'Id', '1', '1', '0', '0', '1', '1', '0', '0', '0', '0', '0', NULL, 'EQ', 'input-number', '', '0', NULL, 1, 1, NULL, '2024-05-10 21:45:32', NULL, '0');
INSERT IGNORE INTO `generator_table_column` (`column_id`, `table_id`, `column_name`, `column_comment`, `column_type`, `java_type`, `search_type`, `ts_type`, `java_type_package`, `java_field`, `up_camel_field`, `is_pk`, `is_increment`, `is_required`, `is_insert`, `is_edit`, `is_list`, `is_query`, `is_import`, `is_export`, `is_autofill`, `is_unique_valid`, `autofill_type`, `query_type`, `html_type`, `dict_type`, `is_logic_del`, `options`, `sort`, `create_id`, `update_id`, `create_time`, `update_time`, `dict_show_way`) VALUES (2, 1, 'year', '统计年限', 'varchar(4)', 'String', 'input', 'string', NULL, 'year', 'Year', '0', '0', '1', '1', '1', '1', '1', '1', '1', '0', '0', NULL, 'EQ', 'input', '', '0', NULL, 2, 1, NULL, '2024-05-10 21:45:32', NULL, '0');
INSERT IGNORE INTO `generator_table_column` (`column_id`, `table_id`, `column_name`, `column_comment`, `column_type`, `java_type`, `search_type`, `ts_type`, `java_type_package`, `java_field`, `up_camel_field`, `is_pk`, `is_increment`, `is_required`, `is_insert`, `is_edit`, `is_list`, `is_query`, `is_import`, `is_export`, `is_autofill`, `is_unique_valid`, `autofill_type`, `query_type`, `html_type`, `dict_type`, `is_logic_del`, `options`, `sort`, `create_id`, `update_id`, `create_time`, `update_time`, `dict_show_way`) VALUES (3, 1, 'month', '统计月份', 'varchar(2)', 'String', 'input', 'string', NULL, 'month', 'Month', '0', '0', '1', '1', '1', '1', '1', '1', '1', '0', '0', NULL, 'EQ', 'input', '', '0', NULL, 3, 1, NULL, '2024-05-10 21:45:32', NULL, '0');
INSERT IGNORE INTO `generator_table_column` (`column_id`, `table_id`, `column_name`, `column_comment`, `column_type`, `java_type`, `search_type`, `ts_type`, `java_type_package`, `java_field`, `up_camel_field`, `is_pk`, `is_increment`, `is_required`, `is_insert`, `is_edit`, `is_list`, `is_query`, `is_import`, `is_export`, `is_autofill`, `is_unique_valid`, `autofill_type`, `query_type`, `html_type`, `dict_type`, `is_logic_del`, `options`, `sort`, `create_id`, `update_id`, `create_time`, `update_time`, `dict_show_way`) VALUES (4, 1, 'during_time', '统计年月', 'varchar(10)', 'String', 'date-picker', 'string', NULL, 'duringTime', 'DuringTime', '0', '0', '0', '1', '1', '1', '1', '1', '1', '0', '0', NULL, 'EQ', 'input', '', '0', NULL, 4, 1, NULL, '2024-05-10 21:45:32', NULL, '0');
INSERT IGNORE INTO `generator_table_column` (`column_id`, `table_id`, `column_name`, `column_comment`, `column_type`, `java_type`, `search_type`, `ts_type`, `java_type_package`, `java_field`, `up_camel_field`, `is_pk`, `is_increment`, `is_required`, `is_insert`, `is_edit`, `is_list`, `is_query`, `is_import`, `is_export`, `is_autofill`, `is_unique_valid`, `autofill_type`, `query_type`, `html_type`, `dict_type`, `is_logic_del`, `options`, `sort`, `create_id`, `update_id`, `create_time`, `update_time`, `dict_show_way`) VALUES (5, 1, 'teacher_id', '教师id', 'varchar(32)', 'String', 'input', 'string', NULL, 'teacherId', 'TeacherId', '0', '0', '1', '1', '1', '1', '1', '1', '1', '0', '0', NULL, 'EQ', 'input', '', '0', NULL, 5, 1, NULL, '2024-05-10 21:45:32', NULL, '0');
INSERT IGNORE INTO `generator_table_column` (`column_id`, `table_id`, `column_name`, `column_comment`, `column_type`, `java_type`, `search_type`, `ts_type`, `java_type_package`, `java_field`, `up_camel_field`, `is_pk`, `is_increment`, `is_required`, `is_insert`, `is_edit`, `is_list`, `is_query`, `is_import`, `is_export`, `is_autofill`, `is_unique_valid`, `autofill_type`, `query_type`, `html_type`, `dict_type`, `is_logic_del`, `options`, `sort`, `create_id`, `update_id`, `create_time`, `update_time`, `dict_show_way`) VALUES (6, 1, 'teacher_common_type', '讲师区分类型', 'int', 'Integer', 'select', 'number', NULL, 'teacherCommonType', 'TeacherCommonType', '0', '0', '1', '1', '1', '1', '1', '1', '1', '0', '0', NULL, 'EQ', 'select', '', '0', NULL, 6, 1, NULL, '2024-05-10 21:45:32', NULL, '0');
INSERT IGNORE INTO `generator_table_column` (`column_id`, `table_id`, `column_name`, `column_comment`, `column_type`, `java_type`, `search_type`, `ts_type`, `java_type_package`, `java_field`, `up_camel_field`, `is_pk`, `is_increment`, `is_required`, `is_insert`, `is_edit`, `is_list`, `is_query`, `is_import`, `is_export`, `is_autofill`, `is_unique_valid`, `autofill_type`, `query_type`, `html_type`, `dict_type`, `is_logic_del`, `options`, `sort`, `create_id`, `update_id`, `create_time`, `update_time`, `dict_show_way`) VALUES (7, 1, 'total_teaching', '授课总数', 'int', 'Integer', 'input', 'number', NULL, 'totalTeaching', 'TotalTeaching', '0', '0', '0', '1', '1', '1', '1', '1', '1', '0', '0', NULL, 'EQ', 'input-number', '', '0', NULL, 7, 1, NULL, '2024-05-10 21:45:32', NULL, '0');
INSERT IGNORE INTO `generator_table_column` (`column_id`, `table_id`, `column_name`, `column_comment`, `column_type`, `java_type`, `search_type`, `ts_type`, `java_type_package`, `java_field`, `up_camel_field`, `is_pk`, `is_increment`, `is_required`, `is_insert`, `is_edit`, `is_list`, `is_query`, `is_import`, `is_export`, `is_autofill`, `is_unique_valid`, `autofill_type`, `query_type`, `html_type`, `dict_type`, `is_logic_del`, `options`, `sort`, `create_id`, `update_id`, `create_time`, `update_time`, `dict_show_way`) VALUES (8, 1, 'total_class_count', '服务班次数', 'int', 'Integer', 'input', 'number', NULL, 'totalClassCount', 'TotalClassCount', '0', '0', '0', '1', '1', '1', '1', '1', '1', '0', '0', NULL, 'EQ', 'input-number', '', '0', NULL, 8, 1, NULL, '2024-05-10 21:45:32', NULL, '0');
INSERT IGNORE INTO `generator_table_column` (`column_id`, `table_id`, `column_name`, `column_comment`, `column_type`, `java_type`, `search_type`, `ts_type`, `java_type_package`, `java_field`, `up_camel_field`, `is_pk`, `is_increment`, `is_required`, `is_insert`, `is_edit`, `is_list`, `is_query`, `is_import`, `is_export`, `is_autofill`, `is_unique_valid`, `autofill_type`, `query_type`, `html_type`, `dict_type`, `is_logic_del`, `options`, `sort`, `create_id`, `update_id`, `create_time`, `update_time`, `dict_show_way`) VALUES (9, 1, 'total_hours', '课时总数', 'decimal(10,2)', 'BigDecimal', 'input', 'number', 'java.math.BigDecimal', 'totalHours', 'TotalHours', '0', '0', '0', '1', '1', '1', '1', '1', '1', '0', '0', NULL, 'EQ', 'input-number', '', '0', NULL, 9, 1, NULL, '2024-05-10 21:45:32', NULL, '0');
INSERT IGNORE INTO `generator_table_column` (`column_id`, `table_id`, `column_name`, `column_comment`, `column_type`, `java_type`, `search_type`, `ts_type`, `java_type_package`, `java_field`, `up_camel_field`, `is_pk`, `is_increment`, `is_required`, `is_insert`, `is_edit`, `is_list`, `is_query`, `is_import`, `is_export`, `is_autofill`, `is_unique_valid`, `autofill_type`, `query_type`, `html_type`, `dict_type`, `is_logic_del`, `options`, `sort`, `create_id`, `update_id`, `create_time`, `update_time`, `dict_show_way`) VALUES (10, 1, 'check_status', '核对状态', 'int', 'Integer', 'select', 'number', NULL, 'checkStatus', 'CheckStatus', '0', '0', '1', '1', '1', '1', '1', '1', '1', '0', '0', NULL, 'EQ', 'radio', '', '0', NULL, 10, 1, NULL, '2024-05-10 21:45:32', NULL, '0');
INSERT IGNORE INTO `generator_table_column` (`column_id`, `table_id`, `column_name`, `column_comment`, `column_type`, `java_type`, `search_type`, `ts_type`, `java_type_package`, `java_field`, `up_camel_field`, `is_pk`, `is_increment`, `is_required`, `is_insert`, `is_edit`, `is_list`, `is_query`, `is_import`, `is_export`, `is_autofill`, `is_unique_valid`, `autofill_type`, `query_type`, `html_type`, `dict_type`, `is_logic_del`, `options`, `sort`, `create_id`, `update_id`, `create_time`, `update_time`, `dict_show_way`) VALUES (11, 1, 'check_time', '核对时间', 'datetime', 'LocalDateTime', 'date-picker', 'string', 'java.time.LocalDateTime', 'checkTime', 'CheckTime', '0', '0', '0', '1', '1', '1', '1', '1', '1', '0', '0', NULL, 'EQ', 'datetime', '', '0', NULL, 11, 1, NULL, '2024-05-10 21:45:32', NULL, '0');
INSERT IGNORE INTO `generator_table_column` (`column_id`, `table_id`, `column_name`, `column_comment`, `column_type`, `java_type`, `search_type`, `ts_type`, `java_type_package`, `java_field`, `up_camel_field`, `is_pk`, `is_increment`, `is_required`, `is_insert`, `is_edit`, `is_list`, `is_query`, `is_import`, `is_export`, `is_autofill`, `is_unique_valid`, `autofill_type`, `query_type`, `html_type`, `dict_type`, `is_logic_del`, `options`, `sort`, `create_id`, `update_id`, `create_time`, `update_time`, `dict_show_way`) VALUES (12, 1, 'create_time', '生成时间', 'datetime', 'LocalDateTime', 'date-picker', 'string', 'java.time.LocalDateTime', 'createTime', 'CreateTime', '0', '0', '0', '0', '0', '0', '0', '0', '0', '1', '0', 'FieldFill.INSERT', 'EQ', 'datetime', '', '0', NULL, 12, 1, NULL, '2024-05-10 21:45:32', NULL, '0');
INSERT IGNORE INTO `generator_table_column` (`column_id`, `table_id`, `column_name`, `column_comment`, `column_type`, `java_type`, `search_type`, `ts_type`, `java_type_package`, `java_field`, `up_camel_field`, `is_pk`, `is_increment`, `is_required`, `is_insert`, `is_edit`, `is_list`, `is_query`, `is_import`, `is_export`, `is_autofill`, `is_unique_valid`, `autofill_type`, `query_type`, `html_type`, `dict_type`, `is_logic_del`, `options`, `sort`, `create_id`, `update_id`, `create_time`, `update_time`, `dict_show_way`) VALUES (13, 1, 'update_time', '更新时间', 'datetime', 'LocalDateTime', 'date-picker', 'string', 'java.time.LocalDateTime', 'updateTime', 'UpdateTime', '0', '0', '0', '0', '0', '0', '0', '0', '0', '1', '0', 'FieldFill.UPDATE', 'EQ', 'datetime', '', '0', NULL, 13, 1, NULL, '2024-05-10 21:45:32', NULL, '0');
INSERT IGNORE INTO `generator_table_column` (`column_id`, `table_id`, `column_name`, `column_comment`, `column_type`, `java_type`, `search_type`, `ts_type`, `java_type_package`, `java_field`, `up_camel_field`, `is_pk`, `is_increment`, `is_required`, `is_insert`, `is_edit`, `is_list`, `is_query`, `is_import`, `is_export`, `is_autofill`, `is_unique_valid`, `autofill_type`, `query_type`, `html_type`, `dict_type`, `is_logic_del`, `options`, `sort`, `create_id`, `update_id`, `create_time`, `update_time`, `dict_show_way`) VALUES (14, 1, 'last_sync_time', '最近一次同步时间', 'datetime', 'LocalDateTime', 'date-picker', 'string', 'java.time.LocalDateTime', 'lastSyncTime', 'LastSyncTime', '0', '0', '0', '1', '1', '1', '1', '1', '1', '0', '0', NULL, 'EQ', 'datetime', '', '0', NULL, 14, 1, NULL, '2024-05-10 21:45:32', NULL, '0');
INSERT IGNORE INTO `generator_table_column` (`column_id`, `table_id`, `column_name`, `column_comment`, `column_type`, `java_type`, `search_type`, `ts_type`, `java_type_package`, `java_field`, `up_camel_field`, `is_pk`, `is_increment`, `is_required`, `is_insert`, `is_edit`, `is_list`, `is_query`, `is_import`, `is_export`, `is_autofill`, `is_unique_valid`, `autofill_type`, `query_type`, `html_type`, `dict_type`, `is_logic_del`, `options`, `sort`, `create_id`, `update_id`, `create_time`, `update_time`, `dict_show_way`) VALUES (15, 1, 'remark', '备注', 'varchar(255)', 'String', 'input', 'string', NULL, 'remark', 'Remark', '0', '0', '0', '1', '1', '1', '0', '1', '1', '0', '0', NULL, 'EQ', 'input', '', '0', NULL, 15, 1, NULL, '2024-05-10 21:45:32', NULL, '0');


