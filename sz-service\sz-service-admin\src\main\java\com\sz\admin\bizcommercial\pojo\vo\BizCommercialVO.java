package com.sz.admin.bizcommercial.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * BizCommercial返回vo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@Schema(description = "BizCommercial返回vo")
public class BizCommercialVO {

    @Schema(description =  "主键唯一")
    private Long commercialId;

    @Schema(description =  "文件ID")
    private Long fileId;

    @Schema(description =  "创建时间")
    private LocalDateTime createTime;

    @Schema(description =  "更新时间")
    private LocalDateTime updateTime;

    @Schema(description =  "显示顺序")
    private Integer sortNum;

    @Schema(description =  "备注")
    private String remark;

    @Schema(description =  "文件路径")
    private String url;

    @Schema(description =  "文件名")
    private String filename;

    @Schema(description = "禁用/启用")
    private String isNot;
}