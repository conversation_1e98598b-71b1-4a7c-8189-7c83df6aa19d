<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sz.admin.bizenterprise.mapper.BizEnterpriseMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.sz.admin.bizenterprise.pojo.po.BizEnterprise">
        <id column="enterprise_id" property="enterpriseId"/>
        <result column="credit_code" property="creditCode"/>
        <result column="leader_contact" property="leaderContact"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        enterprise_id, credit_code, leader_contact, del_flag, remark, create_id, update_id, create_time, update_time
    </sql>

</mapper>
