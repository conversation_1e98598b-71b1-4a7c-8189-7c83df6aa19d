package com.sz.admin.bizenterprise.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * BizEnterprise添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@Schema(description = "BizEnterprise添加DTO")
public class BizEnterpriseCreateDTO {

   @Schema(description =  "统一社会信用代码")
   private String creditCode;

   @Schema(description ="企业名称")
   private String enterpriseName;

   @Schema(description =  "负责人手机号")
   private String leaderContact;

   @Schema(description =  "备注")
   private String remark;

}