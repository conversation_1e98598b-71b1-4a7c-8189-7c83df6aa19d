package com.sz.admin.bizcommercial.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import com.sz.core.common.entity.ApiPageResult;
import com.sz.core.common.entity.ApiResult;
import com.sz.core.common.constant.GlobalConstant;
import com.sz.core.common.entity.PageResult;
import com.sz.core.common.entity.SelectIdsDTO;
import com.sz.admin.bizcommercial.service.BizCommercialService;
import com.sz.admin.bizcommercial.pojo.dto.BizCommercialCreateDTO;
import com.sz.admin.bizcommercial.pojo.dto.BizCommercialUpdateDTO;
import com.sz.admin.bizcommercial.pojo.dto.BizCommercialListDTO;
import com.sz.admin.bizcommercial.pojo.vo.BizCommercialVO;
import com.sz.core.common.entity.ImportExcelDTO;
import jakarta.servlet.http.HttpServletResponse;

import java.sql.SQLException;

/**
 * <p>
 * 广告信息表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Tag(name =  "广告信息表")
@RestController
@RequestMapping("biz-commercial")
@RequiredArgsConstructor
public class BizCommercialController  {

    private final BizCommercialService bizCommercialService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "biz.commercial.create")
    @PostMapping
    public ApiResult<Void> create(@RequestBody BizCommercialCreateDTO dto) {
        bizCommercialService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "biz.commercial.update")
    @PutMapping
    public ApiResult<Void> update(@RequestBody BizCommercialUpdateDTO dto) {
        bizCommercialService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "biz.commercial.remove")
    @DeleteMapping
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        bizCommercialService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "biz.commercial.query_table")
    @GetMapping
    public ApiResult<PageResult<BizCommercialVO>> list(BizCommercialListDTO dto) throws SQLException {
        return ApiPageResult.success(bizCommercialService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "biz.commercial.query_table")
    @GetMapping("/{id}")
    public ApiResult<BizCommercialVO> detail(@PathVariable Object id) {
        return ApiResult.success(bizCommercialService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
      @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "biz.commercial.import")
    @PostMapping("/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        bizCommercialService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "biz.commercial.export")
    @PostMapping("/export")
    public void exportExcel(@RequestBody BizCommercialListDTO dto, HttpServletResponse response) {
        bizCommercialService.exportExcel(dto, response);
    }
}