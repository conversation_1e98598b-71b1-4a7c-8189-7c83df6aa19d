package com.sz.admin.bizpubcontent.pojo.vo;

import com.alibaba.fastjson2.JSONObject;
import com.sz.admin.bizreviewhistory.pojo.po.BizReviewHistory;
import com.sz.admin.bizreviewhistory.pojo.vo.BizReviewHistoryVO;
import com.sz.admin.system.pojo.po.SysFile;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.sz.excel.annotation.DictFormat;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * BizPubContent返回vo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@Schema(description = "BizPubContent返回vo")
public class BizPubContentVO {

    @Schema(description =  "主键唯一")
    private Long pubId;

    @ExcelIgnore
    @Schema(description =  "审核状态(码表)")
    private String reviewStatus;

    @ExcelIgnore
    @Schema(description =  "内容")
    private String pubContent;

    @ExcelIgnore
    @Schema(description =  "创建人ID")
    private Long createId;

    @ExcelIgnore
    @Schema(description =  "创建时间")
    private LocalDateTime createTime;

    @ExcelIgnore
    @Schema(description =  "更新时间")
    private LocalDateTime updateTime;

    @Schema(description =  "联系手机号")
    private String phoneNumber;

    @Schema(description =  "文件列表")
    private String fileList;

    @Schema(description =  "标题")
    private String title;

    @Schema(description =  "文件列表")
    private List<JSONObject> fileListInfo;

    @Schema(description =  "发布人")
    private String publisher;

    @Schema(description =  "内容类型")
    private String pubContentType;

    @Schema(description =  "审核历史")
    private List<BizReviewHistoryVO> reviewHistoryList;

    @Schema(description =  "是否置顶")
    private String isFirst;

    @Schema(description =  "是否上架")
    private String isShow;
}