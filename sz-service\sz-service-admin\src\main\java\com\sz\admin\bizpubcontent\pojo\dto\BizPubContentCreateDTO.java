package com.sz.admin.bizpubcontent.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * BizPubContent添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@Schema(description = "BizPubContent添加DTO")
public class BizPubContentCreateDTO {

   @Schema(description =  "主键唯一")
   private Long pubId;

   @Schema(description =  "审核状态(码表)")
   private String reviewStatus;

   @Schema(description =  "内容")
   private String pubContent;

   @Schema(description =  "联系手机号")
   private String phoneNumber;

   @Schema(description =  "文件列表")
   private String fileList;

   @Schema(description =  "标题")
   private String title;

   @Schema(description =  "内容类型")
   private String pubContentType;

   @Schema(description =  "是否置顶")
   private String isFirst;

   @Schema(description =  "是否上架")
   private String isShow;
}