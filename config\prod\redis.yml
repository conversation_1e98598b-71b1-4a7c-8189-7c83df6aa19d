# redis
spring:
  data:
    redis:
      #单机配置
      host: 127.0.0.1
      port: 6379
      # 数据索引
      database: 0
      # 连接超时时间（毫秒）
      timeout: 50000
      ###连接池配置###
      jedis:
        pool:
          # 连接池最大连接数（使用负值表示没有限制）
          max-active: 100
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1
          # 连接池中的最大空闲连接
          max-idle: 30
          # 连接池中的最小空闲连接
          min-idle: 10
      password: 123456

# 开启redis监听
redis:
  listener:
    enable: true