package com.sz.admin.bizpubcontent.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.sz.admin.bizpubcontent.mapper.BizPubContentMapper;
import com.sz.admin.bizpubcontent.pojo.dto.BizPubContentCreateDTO;
import com.sz.admin.bizpubcontent.pojo.dto.BizPubContentImportDTO;
import com.sz.admin.bizpubcontent.pojo.dto.BizPubContentListDTO;
import com.sz.admin.bizpubcontent.pojo.dto.BizPubContentUpdateDTO;
import com.sz.admin.bizpubcontent.pojo.po.BizPubContent;
import com.sz.admin.bizpubcontent.pojo.vo.BizPubContentVO;
import com.sz.admin.bizpubcontent.service.BizPubContentService;
import com.sz.admin.bizreviewhistory.pojo.po.BizReviewHistory;
import com.sz.admin.bizreviewhistory.pojo.vo.BizReviewHistoryVO;
import com.sz.admin.bizreviewhistory.service.BizReviewHistoryService;
import com.sz.admin.system.service.SysFileService;
import com.sz.core.common.entity.ImportExcelDTO;
import com.sz.core.common.entity.PageResult;
import com.sz.core.common.entity.SelectIdsDTO;
import com.sz.core.common.enums.CommonResponseEnum;
import com.sz.core.util.BeanCopyUtils;
import com.sz.core.util.FileUtils;
import com.sz.core.util.Utils;
import com.sz.excel.core.ExcelResult;
import com.sz.excel.utils.ExcelUtils;
import com.sz.utils.QueryUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.List;

/**
 * <p>
 * 内容发布表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Service
@RequiredArgsConstructor
public class BizPubContentServiceImpl extends ServiceImpl<BizPubContentMapper, BizPubContent> implements BizPubContentService {

    @Resource
    private SysFileService sysFileService;

    @Resource
    private BizReviewHistoryService bizReviewHistoryService;

    @Resource
    private Snowflake snowflake;

    @Override
    public void create(BizPubContentCreateDTO dto) {
        long pubId = snowflake.nextId();
        dto.setPubId(pubId);

        BizPubContent bizPubContent = BeanCopyUtils.copy(dto, BizPubContent.class);
        save(bizPubContent);

        // 如果审核状态为提交,则进入审核历史表
        if (dto.getReviewStatus() != null && "2001002".equals(dto.getReviewStatus())) {
            BizReviewHistory history = new BizReviewHistory();
            history.setPubId(dto.getPubId());
            history.setReviewStatus(dto.getReviewStatus());
            // 这里设置审核人 应该改为审核角色,直接指定为 管理员
            history.setAuditor(1L);
            bizReviewHistoryService.save(history);
        }
    }

    @Override
    public void update(BizPubContentUpdateDTO dto) {
        BizPubContent bizPubContent = BeanCopyUtils.copy(dto, BizPubContent.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
                .eq(BizPubContent::getPubId, dto.getPubId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        if (dto.getReviewStatus() != null && "2001002".equals(dto.getReviewStatus())) {
            BizReviewHistory history = new BizReviewHistory();
            history.setPubId(dto.getPubId());
            history.setReviewStatus(dto.getReviewStatus());
            // 这里设置审核人 应该改为审核角色,直接指定为 管理员
            history.setAuditor(1L);
            bizReviewHistoryService.save(history);
        }
        saveOrUpdate(bizPubContent);
    }

    @Override
    public void updateByAction(BizPubContentUpdateDTO dto) {
        //根据动作执行不同的逻辑 delete, reject, agree, take_down, take_up
        switch (dto.getAction()) {
            // 执行删除
            case "delete":
                removeById(dto.getPubId());
                break;
            // 执行驳回
            case "reject":
                // 设置审核状态 待提交
                dto.setReviewStatus("2001001");
                update(dto);

                // 添加到审核历史表
                BizReviewHistory history = new BizReviewHistory();
                history.setPubId(dto.getPubId());
                history.setReviewStatus(dto.getReviewStatus());
                history.setReviewContent(dto.getReviewContent());
                // 这里设置审核人 应该改为审核角色,直接指定为 管理员
                history.setAuditor(1L);
                bizReviewHistoryService.save(history);

                break;
            // 执行同意
            case "agree":
                // 设置审核状态 审核通过
                dto.setReviewStatus("2001003");
                // 设置准备上架
                dto.setIsShow("2000002");
                update(dto);

                // 添加到审核历史表
                BizReviewHistory history1 = new BizReviewHistory();
                history1.setPubId(dto.getPubId());
                history1.setReviewStatus(dto.getReviewStatus());
                history1.setReviewContent(dto.getReviewContent());
                // 这里设置审核人 应该改为审核角色,直接指定为 管理员
                history1.setAuditor(1L);
                bizReviewHistoryService.save(history1);
                break;
            // 执行下架
            case "take_down":
                // 设置 is_show 为 2000002
                dto.setIsShow("2000002");
                update(dto);
                break;
            // 执行上架
            case "take_up":
                // 设置 is_show 为 2000001
                dto.setIsShow("2000001");
                update(dto);
                break;
            // 执行置顶
                // 设置 is_first 为 2000001
            case "set_first":
                // 设置 is_first 为 2000001
                dto.setIsFirst("2000001");
                update(dto);
                break;
            // 执行取消置顶
                // 设置 is_first 为 2000002
            case "not_first":
                // 设置 is_first 为 2000002
                dto.setIsFirst("2000002");
                update(dto);
                break;
            default:
                break;
        }
    }

    @Override
    public PageResult<BizPubContentVO> page(BizPubContentListDTO dto) throws SQLException {
        QueryWrapper wrapper = QueryWrapper.create().select("bpc.*, su.nickname as publisher").from("biz_pub_content").as("bpc")
                .leftJoin("sys_user").as("su").on("bpc.create_id = su.id")
                .orderBy("bpc.is_first IS NULL ASC")
                .orderBy("field(bpc.is_first,2000001,2000002)",true)
                .orderBy("bpc.create_time desc")
                .where(queryChain -> {
                    if (Utils.isNotNull(dto.getTitle())) {
                        queryChain.like("bpc.title", dto.getTitle());
                    }
                    if (Utils.isNotNull(dto.getReviewStatus())) {
                        queryChain.eq("bpc.review_status", dto.getReviewStatus());
                    }
                    if (Utils.isNotNull(dto.getPublisher())) {
                        queryChain.like("su.nickname", dto.getPublisher());
                    }
                    if (Utils.isNotNull(dto.getPhoneNumber())) {
                        queryChain.like("bpc.phone_number", dto.getPhoneNumber());
                    }
                    if (Utils.isNotNull(dto.getPubContentType())){
                        queryChain.eq("bpc.pub_content_type", dto.getPubContentType());
                    }
                });
        return QueryUtil.page(dto, wrapper, BizPubContentVO.class);
    }


    @Override
    public PageResult<BizPubContentVO> reviewPage(BizPubContentListDTO dto) throws SQLException {
        QueryWrapper wrapper = QueryWrapper.create().select("bpc.*, su.nickname as publisher").from("biz_pub_content").as("bpc")
                .leftJoin("sys_user").as("su").on("bpc.create_id = su.id")
                .orderBy("bpc.is_first IS NULL ASC")
                .orderBy("field(bpc.is_first,2000001,2000002)",true)
                .orderBy("bpc.create_time desc")
                .where(queryChain -> {
                    //仅查询待审核数据
                    queryChain.ne("bpc.review_status", "2001001");

                    if (Utils.isNotNull(dto.getTitle())) {
                        queryChain.eq("bpc.title", dto.getTitle());
                    }
                    if (Utils.isNotNull(dto.getReviewStatus())) {
                        queryChain.eq("bpc.review_status", dto.getReviewStatus());
                    }
                    if (Utils.isNotNull(dto.getPublisher())) {
                        queryChain.like("su.nickname", dto.getPublisher());
                    }
                    if (Utils.isNotNull(dto.getPhoneNumber())) {
                        queryChain.like("bpc.phone_number", dto.getPhoneNumber());
                    }
                });
        return QueryUtil.page(dto, wrapper, BizPubContentVO.class);
    }

    @Override
    public List<BizPubContentVO> list(BizPubContentListDTO dto) {
        return listAs(buildQueryWrapper(dto), BizPubContentVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public BizPubContentVO detail(Object id) throws SQLException {
        BizPubContent bizPubContent = getById((Serializable) id);
        CommonResponseEnum.INVALID_ID.assertNull(bizPubContent);

        BizPubContentVO vo = BeanCopyUtils.copy(bizPubContent, BizPubContentVO.class);

        // 文件列表不为空
        if (StrUtil.isNotEmpty(bizPubContent.getFileList())) {
            List<JSONObject> sysFiles = sysFileService.getByIds(bizPubContent.getFileList());
            vo.setFileListInfo(sysFiles); // 设置vo
        }

        // 发布历史列表不为空
        QueryWrapper queryWrapper = QueryWrapper.create().select("bph.*, su.nickname as auditor_user, cru.nickname as create_user").from("biz_review_history").as("bph")
                .leftJoin("sys_user").as("su").on("bph.auditor = su.id")
                .leftJoin("sys_user").as("cru").on("bph.create_id = cru.id")
                .where(queryChain -> {
                    queryChain.eq("bph.pub_id", id);
                });
        List<BizReviewHistoryVO> bizReviewHistoryVOs = QueryUtil.list(queryWrapper, BizReviewHistoryVO.class);
        vo.setReviewHistoryList(bizReviewHistoryVOs);

        return vo;
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<BizPubContentImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), BizPubContentImportDTO.class, true);
        List<BizPubContentImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(BizPubContentListDTO dto, HttpServletResponse response) {
        List<BizPubContentVO> list = list(dto);
        String fileName = "内容发布表模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "内容发布表", BizPubContentVO.class, os);
    }

    private static QueryWrapper buildQueryWrapper(BizPubContentListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(BizPubContent.class);
        if (Utils.isNotNull(dto.getReviewStatus())) {
            wrapper.eq(BizPubContent::getReviewStatus, dto.getReviewStatus());
        }
        if (Utils.isNotNull(dto.getPubContent())) {
            wrapper.like(BizPubContent::getPubContent, dto.getPubContent());
        }
        if (Utils.isNotNull(dto.getCreateTime())) {
            wrapper.eq(BizPubContent::getCreateTime, dto.getCreateTime());
        }
        return wrapper;
    }
}