package com.sz.admin.bizagreement.controller;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import com.sz.core.common.entity.ApiPageResult;
import com.sz.core.common.entity.ApiResult;
import com.sz.core.common.constant.GlobalConstant;
import com.sz.core.common.entity.PageResult;
import com.sz.core.common.entity.SelectIdsDTO;
import com.sz.admin.bizagreement.service.BizAgreementService;
import com.sz.admin.bizagreement.pojo.dto.BizAgreementCreateDTO;
import com.sz.admin.bizagreement.pojo.dto.BizAgreementUpdateDTO;
import com.sz.admin.bizagreement.pojo.dto.BizAgreementListDTO;
import com.sz.admin.bizagreement.pojo.vo.BizAgreementVO;
import com.sz.core.common.entity.ImportExcelDTO;
import jakarta.servlet.http.HttpServletResponse;

import java.sql.SQLException;

/**
 * <p>
 * 公示/协议内容表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Tag(name =  "公示/协议内容表")
@RestController
@RequestMapping("biz-agreement")
@RequiredArgsConstructor
public class BizAgreementController  {

    private final BizAgreementService bizAgreementService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "biz.agreement.create")
    @PostMapping
    public ApiResult<Void> create(@RequestBody BizAgreementCreateDTO dto) {
        bizAgreementService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "biz.agreement.update")
    @PutMapping
    public ApiResult<Void> update(@RequestBody BizAgreementUpdateDTO dto) {
        bizAgreementService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "biz.agreement.remove")
    @DeleteMapping
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        bizAgreementService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "biz.agreement.query_table")
    @GetMapping
    public ApiResult<PageResult<BizAgreementVO>> list(BizAgreementListDTO dto) {
        return ApiPageResult.success(bizAgreementService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "biz.agreement.query_table")
    @GetMapping("/{id}")
    public ApiResult<BizAgreementVO> detail(@PathVariable Object id) {
        return ApiResult.success(bizAgreementService.detail(id));
    }

    @Operation(summary = "详情")
    @PostMapping("/getDetailById")
    public ApiResult<BizAgreementVO> getDetailById(@RequestBody JSONObject jsonObject) {
        return ApiResult.success(bizAgreementService.detail(jsonObject.getString("agreementId")));
    }

    @Operation(summary = "导入")
    @Parameters({
      @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "biz.agreement.import")
    @PostMapping("/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        bizAgreementService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "biz.agreement.export")
    @PostMapping("/export")
    public void exportExcel(@RequestBody BizAgreementListDTO dto, HttpServletResponse response) {
        bizAgreementService.exportExcel(dto, response);
    }

    @Operation(summary = "小程序首页")
    @PostMapping("/getMiniAppHome")
    public ApiResult<JSONObject> getMiniAppHome(@RequestBody JSONObject params) throws SQLException {
        return ApiResult.success(bizAgreementService.getMiniAppHome(params));
    }

}