package com.sz.admin.bizenterprise.pojo.po;

import com.mybatisflex.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.io.Serial;
import com.sz.mysql.EntityChangeListener;
import java.time.LocalDateTime;

/**
* <p>
* 企业信息表
* </p>
*
* <AUTHOR>
* @since 2025-05-24
*/
@Data
@Table(value = "biz_enterprise", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "企业信息表")
public class BizEnterprise implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Schema(description ="使用用户表ID")
    private Long enterpriseId;

    @Schema(description ="企业名称")
    private String enterpriseName;

    @Schema(description ="统一社会信用代码")
    private String creditCode;

    @Schema(description ="负责人手机号")
    private String leaderContact;

    @Schema(description ="删除标识")
    private String delFlag;

    @Schema(description ="备注")
    private String remark;

    @Schema(description ="创建人ID")
    private Long createId;

    @Schema(description ="更新人ID")
    private Long updateId;

    @Schema(description ="创建时间")
    private LocalDateTime createTime;

    @Schema(description ="更新时间")
    private LocalDateTime updateTime;

}