package com.sz.admin.bizcommercial.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * BizCommercial修改DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@Schema(description = "BizCommercial修改DTO")
public class BizCommercialUpdateDTO {

    @Schema(description =  "主键唯一")
    private Long commercialId;

    @Schema(description =  "文件ID")
    private Long fileId;

    @Schema(description =  "显示顺序")
    private Integer sortNum;

    @Schema(description =  "备注")
    private String remark;

    @Schema(description = "禁用/启用")
    private String isNot;
}