<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sz.admin.system.mapper.SysRoleMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sz.admin.system.pojo.po.SysRoleMenu">
        <id column="id" property="id"/>
        <result column="menu_id" property="menuId"/>
        <result column="role_id" property="roleId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, menu_id, role_id
    </sql>

    <!-- 批量插入角色菜单关联记录 -->
    <insert id="insertBatchSysRoleMenu">
        INSERT INTO sys_role_menu (role_id, menu_id)
        VALUES
        <foreach collection="menuIds" item="menuId" separator=",">
            (#{roleId}, #{menuId})
        </foreach>
    </insert>

</mapper>
