package com.sz.admin.bizagreement.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.sz.core.common.entity.PageQuery;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * <p>
 * BizAgreement查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@Schema(description = "BizAgreement查询DTO")
public class BizAgreementListDTO extends PageQuery {

    @Schema(description =  "标题")
    private String title;

    @Schema(description =  "公示/协议 内容")
    private String agreementContent;

    @Schema(description =  "启用/禁用")
    private String isNot;

    @Schema(description =  "排序")
    private Integer sortNum;

    @Schema(description =  "业务类型")
    private String agreementType;
}