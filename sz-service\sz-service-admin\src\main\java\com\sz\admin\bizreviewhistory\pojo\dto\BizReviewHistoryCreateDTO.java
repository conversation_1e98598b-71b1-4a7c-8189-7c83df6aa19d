package com.sz.admin.bizreviewhistory.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * BizReviewHistory添加DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@Schema(description = "BizReviewHistory添加DTO")
public class BizReviewHistoryCreateDTO {

   @Schema(description =  "审核状态")
   private String reviewStatus;

   @Schema(description =  "审核人")
   private Long auditor;

   @Schema(description =  "备注")
   private String remark;

   @Schema(description =  "审核内容")
   private String reviewContent;
}