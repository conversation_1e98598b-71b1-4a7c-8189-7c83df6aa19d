package com.sz.admin.bizcommercial.service;

import com.mybatisflex.core.service.IService;
import com.sz.admin.bizcommercial.pojo.po.BizCommercial;
import com.sz.core.common.entity.SelectIdsDTO;
import com.sz.core.common.entity.PageResult;

import java.sql.SQLException;
import java.util.List;
import com.sz.admin.bizcommercial.pojo.dto.BizCommercialCreateDTO;
import com.sz.admin.bizcommercial.pojo.dto.BizCommercialUpdateDTO;
import com.sz.admin.bizcommercial.pojo.dto.BizCommercialListDTO;
import com.sz.admin.bizcommercial.pojo.vo.BizCommercialVO;
import com.sz.core.common.entity.ImportExcelDTO;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <p>
 * 广告信息表 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
public interface BizCommercialService extends IService<BizCommercial> {

    void create(BizCommercialCreateDTO dto);

    void update(BizCommercialUpdateDTO dto);

    PageResult<BizCommercialVO> page(BizCommercialListDTO dto) throws SQLException;

    List<BizCommercialVO> list(BizCommercialListDTO dto);

    void remove(SelectIdsDTO dto);

    BizCommercialVO detail(Object id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(BizCommercialListDTO dto, HttpServletResponse response);
}