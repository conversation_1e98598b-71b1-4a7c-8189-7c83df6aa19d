package com.sz.admin.bizcommercial.service.impl;

import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.sz.admin.system.pojo.po.SysFile;
import com.sz.admin.system.service.SysFileService;
import com.sz.utils.QueryUtil;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.sz.admin.bizcommercial.service.BizCommercialService;
import com.sz.admin.bizcommercial.pojo.po.BizCommercial;
import com.sz.admin.bizcommercial.mapper.BizCommercialMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.query.QueryChain;
import com.sz.core.common.enums.CommonResponseEnum;
import com.sz.core.util.PageUtils;
import com.sz.core.util.BeanCopyUtils;
import com.sz.core.util.Utils;
import com.sz.core.common.entity.PageResult;
import com.sz.core.common.entity.SelectIdsDTO;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.List;
import com.sz.admin.bizcommercial.pojo.dto.BizCommercialCreateDTO;
import com.sz.admin.bizcommercial.pojo.dto.BizCommercialUpdateDTO;
import com.sz.admin.bizcommercial.pojo.dto.BizCommercialListDTO;
import com.sz.admin.bizcommercial.pojo.dto.BizCommercialImportDTO;
import com.sz.core.common.entity.ImportExcelDTO;
import com.sz.excel.core.ExcelResult;
import java.io.OutputStream;
import jakarta.servlet.http.HttpServletResponse;
import com.sz.core.util.FileUtils;
import com.sz.excel.utils.ExcelUtils;
import lombok.SneakyThrows;
import com.sz.admin.bizcommercial.pojo.vo.BizCommercialVO;

/**
 * <p>
 * 广告信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Service
@RequiredArgsConstructor
public class BizCommercialServiceImpl extends ServiceImpl<BizCommercialMapper, BizCommercial> implements BizCommercialService {

    @Resource
    private SysFileService sysFileService;

    @Override
    public void create(BizCommercialCreateDTO dto){
        BizCommercial bizCommercial = BeanCopyUtils.copy(dto, BizCommercial.class);
        save(bizCommercial);
    }

    @Override
    public void update(BizCommercialUpdateDTO dto){
        BizCommercial bizCommercial = BeanCopyUtils.copy(dto, BizCommercial.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
            .eq(BizCommercial::getCommercialId, dto.getCommercialId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        saveOrUpdate(bizCommercial);
    }

    @Override
    public PageResult<BizCommercialVO> page(BizCommercialListDTO dto) throws SQLException {
        QueryWrapper wrapper = QueryWrapper.create().select("bc.*, sf.url, sf.filename").from("biz_commercial").as("bc")
            .leftJoin("sys_file").as("sf").on("bc.file_id = sf.id")
                .orderBy("bc.sort_num asc")
            .where(wp -> {
                if (Utils.isNotNull(dto.getFilename())) {
                    wp.like("sf.filename", dto.getFilename());
                }
            });
        return QueryUtil.page(dto, wrapper, BizCommercialVO.class);
    }

    @Override
    public List<BizCommercialVO> list(BizCommercialListDTO dto){
        return listAs(buildQueryWrapper(dto), BizCommercialVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto){
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public BizCommercialVO detail(Object id){
        BizCommercial bizCommercial = getById((Serializable) id);
        CommonResponseEnum.INVALID_ID.assertNull(bizCommercial);

        SysFile file = sysFileService.getById(bizCommercial.getFileId());
        BizCommercialVO bizCommercialVO = BeanCopyUtils.copy(bizCommercial, BizCommercialVO.class);
        bizCommercialVO.setUrl(file.getUrl());
        bizCommercialVO.setFilename(file.getFilename());
        return bizCommercialVO;
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<BizCommercialImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), BizCommercialImportDTO.class, true);
        List<BizCommercialImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(BizCommercialListDTO dto, HttpServletResponse response) {
        List<BizCommercialVO> list = list(dto);
        String fileName = "广告信息表模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "广告信息表", BizCommercialVO.class, os);
    }

    private static QueryWrapper buildQueryWrapper(BizCommercialListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(BizCommercial.class);
        if (Utils.isNotNull(dto.getFilename())) {
            wrapper.eq(BizCommercial::getCreateTime, dto.getFilename());
        }
        return wrapper;
    }
}