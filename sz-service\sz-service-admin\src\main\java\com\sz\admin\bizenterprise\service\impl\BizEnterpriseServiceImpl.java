package com.sz.admin.bizenterprise.service.impl;

import com.mybatisflex.spring.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.sz.admin.bizenterprise.service.BizEnterpriseService;
import com.sz.admin.bizenterprise.pojo.po.BizEnterprise;
import com.sz.admin.bizenterprise.mapper.BizEnterpriseMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.query.QueryChain;
import com.sz.core.common.enums.CommonResponseEnum;
import com.sz.core.util.PageUtils;
import com.sz.core.util.BeanCopyUtils;
import com.sz.core.util.Utils;
import com.sz.core.common.entity.PageResult;
import com.sz.core.common.entity.SelectIdsDTO;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.List;
import com.sz.admin.bizenterprise.pojo.dto.BizEnterpriseCreateDTO;
import com.sz.admin.bizenterprise.pojo.dto.BizEnterpriseUpdateDTO;
import com.sz.admin.bizenterprise.pojo.dto.BizEnterpriseListDTO;
import com.sz.admin.bizenterprise.pojo.dto.BizEnterpriseImportDTO;
import com.sz.core.common.entity.ImportExcelDTO;
import com.sz.excel.core.ExcelResult;
import java.io.OutputStream;
import jakarta.servlet.http.HttpServletResponse;
import com.sz.core.util.FileUtils;
import com.sz.excel.utils.ExcelUtils;
import com.sz.utils.QueryUtil;

import lombok.SneakyThrows;
import com.sz.admin.bizenterprise.pojo.vo.BizEnterpriseVO;
import com.sz.admin.system.pojo.po.SysUser;
import com.sz.admin.system.mapper.SysUserMapper;

/**
 * <p>
 * 企业信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Service
@RequiredArgsConstructor
public class BizEnterpriseServiceImpl extends ServiceImpl<BizEnterpriseMapper, BizEnterprise> implements BizEnterpriseService {
    
    // 新增用户Mapper依赖注入
    private final SysUserMapper sysUserMapper;

    @Override
    public void create(BizEnterpriseCreateDTO dto){
        // 新增系统用户创建逻辑
        SysUser sysUser = new SysUser();
        sysUser.setUsername(dto.getLeaderContact()); // 使用企业联系人作为用户名
        sysUser.setPwd("123456"); // 设置默认密码
        sysUser.setPhone(dto.getLeaderContact());
        sysUser.setNickname(dto.getEnterpriseName());
        sysUserMapper.insert(sysUser);

        // 设置默认角色,代码待实现


        BizEnterprise bizEnterprise = BeanCopyUtils.copy(dto, BizEnterprise.class);
        // 设置企业ID为用户ID
        bizEnterprise.setEnterpriseId(sysUser.getId());
        save(bizEnterprise);
    }

    @Override
    public void update(BizEnterpriseUpdateDTO dto){
        BizEnterprise bizEnterprise = BeanCopyUtils.copy(dto, BizEnterprise.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
            .eq(BizEnterprise::getEnterpriseId, dto.getEnterpriseId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        saveOrUpdate(bizEnterprise);
    }

    @Override
    public PageResult<BizEnterpriseVO> page(BizEnterpriseListDTO dto) throws SQLException{
        QueryWrapper wrapper = QueryWrapper.create().select("be.*, su.account_status_cd, user.nickname as create_name").from("biz_enterprise").as("be")
        .leftJoin("sys_user").as("su").on("be.enterprise_id = su.id")
        .leftJoin("sys_user").as("user").on("be.create_id = user.id")
        .where(wp -> {
            if (Utils.isNotNull(dto.getCreditCode())) {
                wp.like("be.credit_code", dto.getCreditCode());
            }
            if (Utils.isNotNull(dto.getEnterpriseName())) {
                wp.like("be.enterprise_name", dto.getEnterpriseName());
            }
            if (Utils.isNotNull(dto.getLeaderContact())) {
                wp.like("be.leader_contact", dto.getLeaderContact());
            }
            if (Utils.isNotNull(dto.getAccountStatusCd())) {
                wp.eq("su.account_status_cd", dto.getAccountStatusCd());
            }
        });
    
        return QueryUtil.page(dto, wrapper, BizEnterpriseVO.class);
    }

    @Override
    public List<BizEnterpriseVO> list(BizEnterpriseListDTO dto){
        QueryWrapper wrapper = QueryWrapper.create().select().from("null").as("null")
        .leftJoin("null").on("null")
        .leftJoin("null").on("null");

        return listAs(wrapper, BizEnterpriseVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto){
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());

        // 删除关联的系统用户
        QueryWrapper userWrapper = QueryWrapper.create()
            .in(SysUser::getId, dto.getIds());
        sysUserMapper.deleteByQuery(userWrapper);
    }

    @Override
    public BizEnterpriseVO detail(Object id){
        BizEnterprise bizEnterprise = getById((Serializable) id);
        CommonResponseEnum.INVALID_ID.assertNull(bizEnterprise);
        return BeanCopyUtils.copy(bizEnterprise, BizEnterpriseVO.class);
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<BizEnterpriseImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), BizEnterpriseImportDTO.class, true);
        List<BizEnterpriseImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(BizEnterpriseListDTO dto, HttpServletResponse response) {
        List<BizEnterpriseVO> list = list(dto);
        String fileName = "企业信息表模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "企业信息表", BizEnterpriseVO.class, os);
    }

    private static QueryWrapper buildQueryWrapper(BizEnterpriseListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(BizEnterprise.class);
        if (Utils.isNotNull(dto.getCreditCode())) {
            wrapper.eq(BizEnterprise::getCreditCode, dto.getCreditCode());
        }
        if (Utils.isNotNull(dto.getLeaderContact())) {
            wrapper.eq(BizEnterprise::getLeaderContact, dto.getLeaderContact());
        }
        if (Utils.isNotNull(dto.getCreateTime())) {
            wrapper.eq(BizEnterprise::getCreateTime, dto.getCreateTime());
        }
        return wrapper;
    }
}