<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sz.admin.bizcommercial.mapper.BizCommercialMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.sz.admin.bizcommercial.pojo.po.BizCommercial">
        <id column="commercial_id" property="commercialId"/>
        <result column="file_id" property="fileId"/>
        <result column="create_id" property="createId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
        <result column="sort_num" property="sortNum"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        commercial_id, file_id, create_id, create_time, update_id, update_time, sort_num, remark
    </sql>

</mapper>
