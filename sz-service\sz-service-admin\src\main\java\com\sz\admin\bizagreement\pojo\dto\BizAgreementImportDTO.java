package com.sz.admin.bizagreement.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

import cn.idev.excel.annotation.ExcelProperty;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * <p>
 * BizAgreement导入DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@Schema(description = "BizAgreement导入DTO")
public class BizAgreementImportDTO {

    @ExcelProperty(value = "公示/协议 内容")
    @Schema(description =  "公示/协议 内容")
    private String agreementContent;

    @ExcelProperty(value = "启用/禁用")
    @Schema(description =  "启用/禁用")
    private String isNot;

    @ExcelProperty(value = "备注")
    @Schema(description =  "备注")
    private String remark;

    @ExcelProperty(value = "排序")
    @Schema(description =  "排序")
    private Integer sortNum;

}