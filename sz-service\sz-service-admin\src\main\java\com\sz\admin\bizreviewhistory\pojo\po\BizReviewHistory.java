package com.sz.admin.bizreviewhistory.pojo.po;

import com.mybatisflex.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.io.Serial;
import com.sz.mysql.EntityChangeListener;
import java.time.LocalDateTime;

/**
* <p>
* 审核历史表
* </p>
*
* <AUTHOR>
* @since 2025-05-24
*/
@Data
@Table(value = "biz_review_history", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "审核历史表")
public class BizReviewHistory implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Schema(description ="主键唯一")
    private Long reviewHistoryId;

    @Schema(description ="审核状态")
    private String reviewStatus;

    @Schema(description ="审核人")
    private Long auditor;

    @Schema(description ="发布内容ID")
    private Long pubId;

    @Schema(description ="删除标识")
    private String delFlag;

    @Schema(description ="备注")
    private String remark;

    @Schema(description ="创建人ID")
    private Long createId;

    @Schema(description ="更新人ID")
    private Long updateId;

    @Schema(description ="创建时间")
    private LocalDateTime createTime;

    @Schema(description ="更新时间")
    private LocalDateTime updateTime;

    @Schema(description =  "审核内容")
    private String reviewContent;

}