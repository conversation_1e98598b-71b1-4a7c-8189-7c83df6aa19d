package com.sz.admin.bizpubcontent.controller;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import com.sz.core.common.entity.ApiPageResult;
import com.sz.core.common.entity.ApiResult;
import com.sz.core.common.constant.GlobalConstant;
import com.sz.core.common.entity.PageResult;
import com.sz.core.common.entity.SelectIdsDTO;
import com.sz.admin.bizpubcontent.service.BizPubContentService;
import com.sz.admin.bizpubcontent.pojo.dto.BizPubContentCreateDTO;
import com.sz.admin.bizpubcontent.pojo.dto.BizPubContentUpdateDTO;
import com.sz.admin.bizpubcontent.pojo.dto.BizPubContentListDTO;
import com.sz.admin.bizpubcontent.pojo.vo.BizPubContentVO;
import com.sz.core.common.entity.ImportExcelDTO;
import jakarta.servlet.http.HttpServletResponse;

import java.sql.SQLException;

/**
 * <p>
 * 内容发布表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Tag(name =  "内容发布表")
@RestController
@RequestMapping("biz-pub-content")
@RequiredArgsConstructor
public class BizPubContentController  {

    private final BizPubContentService bizPubContentService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "biz.pub.content.create")
    @PostMapping
    public ApiResult<Void> create(@RequestBody BizPubContentCreateDTO dto) {
        bizPubContentService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "biz.pub.content.update")
    @PutMapping
    public ApiResult<Void> update(@RequestBody BizPubContentUpdateDTO dto) {
        bizPubContentService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "biz.pub.content.remove")
    @DeleteMapping
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        bizPubContentService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "biz.pub.content.query_table")
    @GetMapping
    public ApiResult<PageResult<BizPubContentVO>> list(BizPubContentListDTO dto) throws SQLException {
        return ApiPageResult.success(bizPubContentService.page(dto));
    }

    @Operation(summary = "列表查询")
    @PostMapping("/list")
    public ApiResult<PageResult<BizPubContentVO>> listEx(@RequestBody BizPubContentListDTO dto) throws SQLException {
        return ApiPageResult.success(bizPubContentService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "biz.pub.content.query_table")
    @GetMapping("/{id}")
    public ApiResult<BizPubContentVO> detail(@PathVariable Object id) throws SQLException {
        return ApiResult.success(bizPubContentService.detail(id));
    }

    @Operation(summary = "详情")
    @PostMapping("/getDetailById")
    public ApiResult<BizPubContentVO> getDetailById(@RequestBody JSONObject jsonObject) throws SQLException {
        return ApiResult.success(bizPubContentService.detail(jsonObject.getLong("pubId")));
    }

    @Operation(summary = "导入")
    @Parameters({
      @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "biz.pub.content.import")
    @PostMapping("/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        bizPubContentService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "biz.pub.content.export")
    @PostMapping("/export")
    public void exportExcel(@RequestBody BizPubContentListDTO dto, HttpServletResponse response) {
        bizPubContentService.exportExcel(dto, response);
    }
}