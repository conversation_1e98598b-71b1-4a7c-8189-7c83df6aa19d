package com.sz.admin.bizcommercial.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.sz.core.common.entity.PageQuery;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * <p>
 * BizCommercial查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@Schema(description = "BizCommercial查询DTO")
public class BizCommercialListDTO extends PageQuery {

    @Schema(description =  "文件名")
    private String filename;

    @Schema(description = "禁用/启用")
    private String isNot;
}