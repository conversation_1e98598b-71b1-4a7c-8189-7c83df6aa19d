package com.sz.admin.bizpubcontent.pojo.po;

import com.mybatisflex.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.io.Serial;
import com.sz.mysql.EntityChangeListener;
import java.time.LocalDateTime;

/**
* <p>
* 内容发布表
* </p>
*
* <AUTHOR>
* @since 2025-05-24
*/
@Data
@Table(value = "biz_pub_content", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "内容发布表")
public class BizPubContent implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Schema(description ="")
    private Long pubId;

    @Schema(description ="审核状态(码表)")
    private String reviewStatus;

    @Schema(description ="内容")
    private String pubContent;

    @Schema(description ="删除标识")
    private String delFlag;

    @Schema(description ="备注")
    private String remark;

    @Schema(description ="创建人ID")
    private Long createId;

    @Schema(description ="更新人ID")
    private Long updateId;

    @Schema(description ="创建时间")
    private LocalDateTime createTime;

    @Schema(description ="更新时间")
    private LocalDateTime updateTime;

    @Schema(description ="联系手机号")
    private String phoneNumber;

    @Schema(description =  "文件列表")
    private String fileList;

    @Schema(description =  "标题")
    private String title;

    @Schema(description =  "内容类型")
    private String pubContentType;

    @Schema(description =  "是否置顶")
    private String isFirst;

    @Schema(description =  "是否上架")
    private String isShow;
}