<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sz.admin.system.mapper.SysMessageUserMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.sz.admin.system.pojo.po.SysMessageUser">
        <id column="id" property="id"/>
        <result column="message_id" property="messageId"/>
        <result column="receiver_id" property="receiverId"/>
        <result column="is_read" property="isRead"/>
        <result column="read_time" property="readTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, message_id, receiver_id, is_read, read_time, del_flag
    </sql>

</mapper>
