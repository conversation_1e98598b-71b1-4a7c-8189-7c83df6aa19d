package com.sz.admin.bizenterprise.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.sz.core.common.entity.PageQuery;
import java.time.LocalDateTime;
/**
 * <p>
 * BizEnterprise查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@Schema(description = "BizEnterprise查询DTO")
public class BizEnterpriseListDTO extends PageQuery {

    @Schema(description =  "账号状态")
    private String accountStatusCd;

    @Schema(description =  "统一社会信用代码")
    private String creditCode;

    @Schema(description ="企业名称")
    private String enterpriseName;

    @Schema(description =  "负责人手机号")
    private String leaderContact;

    @Schema(description =  "创建时间")
    private LocalDateTime createTime;

}