package com.sz.admin.bizreviewhistory.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import com.sz.core.common.entity.ApiPageResult;
import com.sz.core.common.entity.ApiResult;
import com.sz.core.common.constant.GlobalConstant;
import com.sz.core.common.entity.PageResult;
import com.sz.core.common.entity.SelectIdsDTO;
import com.sz.admin.bizreviewhistory.service.BizReviewHistoryService;
import com.sz.admin.bizreviewhistory.pojo.dto.BizReviewHistoryCreateDTO;
import com.sz.admin.bizreviewhistory.pojo.dto.BizReviewHistoryUpdateDTO;
import com.sz.admin.bizreviewhistory.pojo.dto.BizReviewHistoryListDTO;
import com.sz.admin.bizreviewhistory.pojo.vo.BizReviewHistoryVO;
import com.sz.core.common.entity.ImportExcelDTO;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <p>
 * 审核历史表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Tag(name =  "审核历史表")
@RestController
@RequestMapping("biz-review-history")
@RequiredArgsConstructor
public class BizReviewHistoryController  {

    private final BizReviewHistoryService bizReviewHistoryService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "biz.review.history.create")
    @PostMapping
    public ApiResult<Void> create(@RequestBody BizReviewHistoryCreateDTO dto) {
        bizReviewHistoryService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "biz.review.history.update")
    @PutMapping
    public ApiResult<Void> update(@RequestBody BizReviewHistoryUpdateDTO dto) {
        bizReviewHistoryService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "biz.review.history.remove")
    @DeleteMapping
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        bizReviewHistoryService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "biz.review.history.query_table")
    @GetMapping
    public ApiResult<PageResult<BizReviewHistoryVO>> list(BizReviewHistoryListDTO dto) {
        return ApiPageResult.success(bizReviewHistoryService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "biz.review.history.query_table")
    @GetMapping("/{id}")
    public ApiResult<BizReviewHistoryVO> detail(@PathVariable Object id) {
        return ApiResult.success(bizReviewHistoryService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
      @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "biz.review.history.import")
    @PostMapping("/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        bizReviewHistoryService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "biz.review.history.export")
    @PostMapping("/export")
    public void exportExcel(@RequestBody BizReviewHistoryListDTO dto, HttpServletResponse response) {
        bizReviewHistoryService.exportExcel(dto, response);
    }
}