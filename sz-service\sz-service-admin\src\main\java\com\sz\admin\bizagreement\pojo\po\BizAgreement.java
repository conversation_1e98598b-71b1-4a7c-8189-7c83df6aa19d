package com.sz.admin.bizagreement.pojo.po;

import com.mybatisflex.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.io.Serial;
import com.sz.mysql.EntityChangeListener;
import java.time.LocalDateTime;

/**
* <p>
* 公示/协议内容表
* </p>
*
* <AUTHOR>
* @since 2025-05-24
*/
@Data
@Table(value = "biz_agreement", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "公示/协议内容表")
public class BizAgreement implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Schema(description ="主键唯一")
    private Long agreementId;

    @Schema(description ="标题")
    private String title;

    @Schema(description ="公示/协议 内容")
    private String agreementContent;

    @Schema(description ="启用/禁用")
    private String isNot;

    @Schema(description ="创建人ID")
    private Long createId;

    @Schema(description ="创建时间")
    private LocalDateTime createTime;

    @Schema(description ="更新人ID")
    private Long updateId;

    @Schema(description ="更新时间")
    private LocalDateTime updateTime;

    @Schema(description ="备注")
    private String remark;

    @Schema(description ="排序")
    private Integer sortNum;

    @Schema(description ="业务类型")
    private String agreementType;
}