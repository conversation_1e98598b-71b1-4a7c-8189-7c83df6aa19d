package com.sz.admin.bizreviewhistory.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * BizReviewHistory返回vo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@Schema(description = "BizReviewHistory返回vo")
public class BizReviewHistoryVO {

    @Schema(description =  "审核状态")
    private String reviewStatus;

    @Schema(description =  "审核人")
    private Long auditor;

    @Schema(description =  "审核人姓名")
    private String auditorUser;

    @Schema(description =  "发布内容ID")
    private Long pubId;

    @Schema(description =  "备注")
    private String remark;

    @Schema(description =  "创建时间")
    private LocalDateTime createTime;

    @Schema(description =  "审核内容")
    private String reviewContent;

    @Schema(description =  "内容")
    private String pubContent;

    @Schema(description =  "创建人")
    private String createUser;
}