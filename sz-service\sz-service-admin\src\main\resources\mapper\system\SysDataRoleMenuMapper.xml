<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sz.admin.system.mapper.SysDataRoleMenuMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.sz.admin.system.pojo.po.SysDataRoleMenu">
        <id column="id" property="id"/>
        <result column="role_id" property="roleId"/>
        <result column="menu_id" property="menuId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, role_id, menu_id
    </sql>

</mapper>
