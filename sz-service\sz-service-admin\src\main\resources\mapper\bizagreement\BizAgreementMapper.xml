<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sz.admin.bizagreement.mapper.BizAgreementMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.sz.admin.bizagreement.pojo.po.BizAgreement">
        <id column="agreement_id" property="agreementId"/>
        <result column="agreement_content" property="agreementContent"/>
        <result column="is_not" property="isNot"/>
        <result column="create_id" property="createId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
        <result column="sort_num" property="sortNum"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        agreement_id, agreement_content, is_not, create_id, create_time, update_id, update_time, remark, sort_num
    </sql>

</mapper>
