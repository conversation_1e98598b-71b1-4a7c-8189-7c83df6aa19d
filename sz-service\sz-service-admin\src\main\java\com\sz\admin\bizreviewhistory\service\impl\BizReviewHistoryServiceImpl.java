package com.sz.admin.bizreviewhistory.service.impl;

import com.mybatisflex.spring.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.sz.admin.bizreviewhistory.service.BizReviewHistoryService;
import com.sz.admin.bizreviewhistory.pojo.po.BizReviewHistory;
import com.sz.admin.bizreviewhistory.mapper.BizReviewHistoryMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.query.QueryChain;
import com.sz.core.common.enums.CommonResponseEnum;
import com.sz.core.util.PageUtils;
import com.sz.core.util.BeanCopyUtils;
import com.sz.core.util.Utils;
import com.sz.core.common.entity.PageResult;
import com.sz.core.common.entity.SelectIdsDTO;
import java.io.Serializable;
import java.util.List;
import com.sz.admin.bizreviewhistory.pojo.dto.BizReviewHistoryCreateDTO;
import com.sz.admin.bizreviewhistory.pojo.dto.BizReviewHistoryUpdateDTO;
import com.sz.admin.bizreviewhistory.pojo.dto.BizReviewHistoryListDTO;
import com.sz.admin.bizreviewhistory.pojo.dto.BizReviewHistoryImportDTO;
import com.sz.core.common.entity.ImportExcelDTO;
import com.sz.excel.core.ExcelResult;
import java.io.OutputStream;
import jakarta.servlet.http.HttpServletResponse;
import com.sz.core.util.FileUtils;
import com.sz.excel.utils.ExcelUtils;
import lombok.SneakyThrows;
import com.sz.admin.bizreviewhistory.pojo.vo.BizReviewHistoryVO;

/**
 * <p>
 * 审核历史表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Service
@RequiredArgsConstructor
public class BizReviewHistoryServiceImpl extends ServiceImpl<BizReviewHistoryMapper, BizReviewHistory> implements BizReviewHistoryService {
    @Override
    public void create(BizReviewHistoryCreateDTO dto){
        BizReviewHistory bizReviewHistory = BeanCopyUtils.copy(dto, BizReviewHistory.class);
        save(bizReviewHistory);
    }

    @Override
    public void update(BizReviewHistoryUpdateDTO dto){
        BizReviewHistory bizReviewHistory = BeanCopyUtils.copy(dto, BizReviewHistory.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
            .eq(BizReviewHistory::getReviewHistoryId, dto.getReviewHistoryId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        saveOrUpdate(bizReviewHistory);
    }

    @Override
    public PageResult<BizReviewHistoryVO> page(BizReviewHistoryListDTO dto){
        Page<BizReviewHistoryVO> page = pageAs(PageUtils.getPage(dto), buildQueryWrapper(dto), BizReviewHistoryVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<BizReviewHistoryVO> list(BizReviewHistoryListDTO dto){
        return listAs(buildQueryWrapper(dto), BizReviewHistoryVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto){
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public BizReviewHistoryVO detail(Object id){
        BizReviewHistory bizReviewHistory = getById((Serializable) id);
        CommonResponseEnum.INVALID_ID.assertNull(bizReviewHistory);
        return BeanCopyUtils.copy(bizReviewHistory, BizReviewHistoryVO.class);
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<BizReviewHistoryImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), BizReviewHistoryImportDTO.class, true);
        List<BizReviewHistoryImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(BizReviewHistoryListDTO dto, HttpServletResponse response) {
        List<BizReviewHistoryVO> list = list(dto);
        String fileName = "审核历史表模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "审核历史表", BizReviewHistoryVO.class, os);
    }

    private static QueryWrapper buildQueryWrapper(BizReviewHistoryListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(BizReviewHistory.class);
        if (Utils.isNotNull(dto.getReviewStatus())) {
            wrapper.eq(BizReviewHistory::getReviewStatus, dto.getReviewStatus());
        }
        if (Utils.isNotNull(dto.getAuditor())) {
            wrapper.eq(BizReviewHistory::getAuditor, dto.getAuditor());
        }
        if (Utils.isNotNull(dto.getPubId())) {
            wrapper.like(BizReviewHistory::getPubId, dto.getPubId());
        }
        if (Utils.isNotNull(dto.getCreateTime())) {
            wrapper.eq(BizReviewHistory::getCreateTime, dto.getCreateTime());
        }
        return wrapper;
    }
}