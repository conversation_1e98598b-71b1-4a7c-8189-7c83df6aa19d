package com.sz.admin.bizreviewhistory.service;

import com.mybatisflex.core.service.IService;
import com.sz.admin.bizreviewhistory.pojo.po.BizReviewHistory;
import com.sz.core.common.entity.SelectIdsDTO;
import com.sz.core.common.entity.PageResult;
import java.util.List;
import com.sz.admin.bizreviewhistory.pojo.dto.BizReviewHistoryCreateDTO;
import com.sz.admin.bizreviewhistory.pojo.dto.BizReviewHistoryUpdateDTO;
import com.sz.admin.bizreviewhistory.pojo.dto.BizReviewHistoryListDTO;
import com.sz.admin.bizreviewhistory.pojo.vo.BizReviewHistoryVO;
import com.sz.core.common.entity.ImportExcelDTO;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <p>
 * 审核历史表 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
public interface BizReviewHistoryService extends IService<BizReviewHistory> {

    void create(BizReviewHistoryCreateDTO dto);

    void update(BizReviewHistoryUpdateDTO dto);

    PageResult<BizReviewHistoryVO> page(BizReviewHistoryListDTO dto);

    List<BizReviewHistoryVO> list(BizReviewHistoryListDTO dto);

    void remove(SelectIdsDTO dto);

    BizReviewHistoryVO detail(Object id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(BizReviewHistoryListDTO dto, HttpServletResponse response);
}