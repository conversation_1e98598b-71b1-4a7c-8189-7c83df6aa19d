package com.sz.admin.system.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.sz.admin.system.mapper.CommonFileMapper;
import com.sz.admin.system.pojo.dto.sysfile.SysFileListDTO;
import com.sz.admin.system.pojo.po.SysFile;
import com.sz.admin.system.pojo.po.table.SysFileTableDef;
import com.sz.admin.system.service.SysFileService;
import com.sz.core.common.entity.PageResult;
import com.sz.core.util.BeanCopyUtils;
import com.sz.core.util.PageUtils;
import com.sz.core.util.Utils;
import com.sz.oss.UploadResult;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysFileServiceImpl extends ServiceImpl<CommonFileMapper, SysFile> implements SysFileService {

    @Value("${fileRoot}")
    private String fileRoot;

    @Value("${downloadUrl}")
    private String downloadUrl;

    @Resource
    private Snowflake snowflake;

    /**
     * 文件列表
     *
     * @param dto dto
     * @return {@link PageResult}<{@link SysFile}>
     */
    @Override
    public PageResult<SysFile> fileList(SysFileListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create();
        if (Utils.isNotNull(dto.getFilename())) {
            wrapper.where(SysFileTableDef.SYS_FILE.FILENAME.like(dto.getFilename()));
        }
        wrapper.orderBy(SysFileTableDef.SYS_FILE.CREATE_TIME.desc());
        Page<SysFile> page = page(PageUtils.getPage(dto), wrapper);
        return PageUtils.getPageResult(page);
    }

    /**
     * 上传文件
     *
     * @param file 文件
     * @param type
     * @return {@link String}
     */
    @Override
    public UploadResult uploadFile(MultipartFile file, String type) {
        String fileUrl = "";
//        try {
//            // 文件名
//            String filename = type + "/" + FileUploadUtils.generateFileName(file.getOriginalFilename());
//            // minio 上传文件
//            ObjectWriteResponse objectWriteResponse = minioService.uploadFile(file, filename, file.getContentType());
//            // 获取上传文件url
//            fileUrl = minioService.getPublicObjectUrl(objectWriteResponse.object());
//            Map<String, String> map = new HashMap<>();
//            map.put("filename", filename);
//            map.put("type", type);
//            map.put("url", fileUrl);
//            fileLog(file, map);
//        } catch (Exception e) {
//            e.printStackTrace();
//            AdminResponseEnum.SYS_UPLOAD_FILE_ERROR.assertTrue(true);
//        }
        return UploadResult.builder().build();
    }

    @Override
    public Long fileLog(UploadResult uploadResult) {
        SysFile sysFile = BeanCopyUtils.copy(uploadResult, SysFile.class);
        this.save(sysFile);
        return sysFile.getId();
    }

    /**
     * 上传文件(表单上传)
     *
     * @param file
     * @return {@link JSONObject }
     * @throws IOException
     */
    @Override
    public JSONObject uploadByFormData(MultipartFile file) throws IOException {
        //获取文件的md5值
        String md5 = MD5.create().digestHex(file.getInputStream());
        //查询数据库是否存在相同文件
        SysFile sysFile = getOne(new QueryWrapper().eq("md5", md5));
        if (sysFile!= null) {
            //存在相同文件，直接返回url
            JSONObject result = new JSONObject();
            result.put("id", sysFile.getId());
            result.put("name", sysFile.getFilename());
            result.put("url", sysFile.getUrl());
            return result;
        }else {
            //转为实体类
            sysFile = new SysFile();
            sysFile.setMd5(md5);
            sysFile.setId(snowflake.nextId());
            sysFile.setFilePath(DateUtil.today());
            sysFile.setFilename(file.getOriginalFilename());
            sysFile.setFileSuffix(FileUtil.getSuffix(file.getOriginalFilename()));
            sysFile.setSize(file.getSize());
            // 创建文件夹
            FileUtil.mkdir(fileRoot + sysFile.getFilePath());

            System.out.println("文件上传路径：" + fileRoot + sysFile.getFilePath() + "/" + sysFile.getId() + "." + sysFile.getFileSuffix());

            // 文件上传
            file.transferTo(FileUtil.file(fileRoot +  sysFile.getFilePath() + "/" + sysFile.getId() + "." + sysFile.getFileSuffix()));
            // 文件上传成功后，记录日志
            sysFile.setUrl(downloadUrl + "?fileId=" + sysFile.getId());
            this.save(sysFile);
            // 返回结果
            JSONObject result = new JSONObject();
            result.put("id", sysFile.getId());
            result.put("name", sysFile.getFilename());
            result.put("url", sysFile.getUrl());
            return result;
        }
    }

    @Override
    public List<JSONObject> getByIds(String ids) {
        if (StrUtil.isNotEmpty(ids)) {
            List<String> idArr = Arrays.asList(ids.split(","));
            List<SysFile> sysFiles = this.list(new QueryWrapper().in("id", idArr));
            return sysFiles.stream().map(sysFile -> {
                JSONObject result = new JSONObject();
                result.put("id", sysFile.getId());
                result.put("name", sysFile.getFilename());
                result.put("url", sysFile.getUrl());
                return result;
            }).toList();
        }
        return List.of();
    }
}
