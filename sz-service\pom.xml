<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sz-boot-parent</artifactId>
        <groupId>com.sz</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sz-service</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>sz-service-admin</module>
        <module>sz-service-websocket</module>
    </modules>

    <!--sz-service模块下的通用依赖-->
    <dependencies>
        <!-- 全局common-core模块 -->
        <dependency>
            <groupId>com.sz</groupId>
            <artifactId>sz-common-core</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- knife4j swagger文档 -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springdoc</groupId>
                    <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
    </dependencies>

    <!--springboot maven打包，解决 “jar中没有主清单属性” 的问题-->
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>