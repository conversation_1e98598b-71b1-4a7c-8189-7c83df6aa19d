package com.sz.admin.bizagreement.service;

import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.service.IService;
import com.sz.admin.bizagreement.pojo.po.BizAgreement;
import com.sz.core.common.entity.SelectIdsDTO;
import com.sz.core.common.entity.PageResult;

import java.sql.SQLException;
import java.util.List;
import com.sz.admin.bizagreement.pojo.dto.BizAgreementCreateDTO;
import com.sz.admin.bizagreement.pojo.dto.BizAgreementUpdateDTO;
import com.sz.admin.bizagreement.pojo.dto.BizAgreementListDTO;
import com.sz.admin.bizagreement.pojo.vo.BizAgreementVO;
import com.sz.core.common.entity.ImportExcelDTO;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <p>
 * 公示/协议内容表 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
public interface BizAgreementService extends IService<BizAgreement> {

    void create(BizAgreementCreateDTO dto);

    void update(BizAgreementUpdateDTO dto);

    PageResult<BizAgreementVO> page(BizAgreementListDTO dto);

    List<BizAgreementVO> list(BizAgreementListDTO dto);

    void remove(SelectIdsDTO dto);

    BizAgreementVO detail(Object id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(BizAgreementListDTO dto, HttpServletResponse response);

    JSONObject getMiniAppHome(JSONObject params) throws SQLException;
}