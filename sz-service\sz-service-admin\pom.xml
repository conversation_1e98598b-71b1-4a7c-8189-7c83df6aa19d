<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sz-service</artifactId>
        <groupId>com.sz</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>sz-service-admin</artifactId>

    <!-- 全局公用dependency版本管理 -->
    <properties>
        <flyway.version>10.12.0</flyway.version>
        <liquibase.version>4.31.1</liquibase.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!-- 数据库依赖尽量独立引用，因为每个service引用的数据库有可能不同，因此就不再parent中配置公共的mysql依赖-->
        <!-- mysql数据库模块 -->
        <dependency>
            <groupId>com.sz</groupId>
            <artifactId>sz-common-db-mysql</artifactId>
            <version>${revision}</version>
        </dependency>
        <!--鉴权模块-->
        <dependency>
            <groupId>com.sz</groupId>
            <artifactId>sz-common-security</artifactId>
            <version>${revision}</version>
        </dependency>
        <!--代码生成模块-->
        <dependency>
            <groupId>com.sz.generator</groupId>
            <artifactId>sz-common-generator</artifactId>
            <version>${revision}</version>
        </dependency>
        <!--微信模块-->
        <dependency>
            <groupId>com.sz.wechat</groupId>
            <artifactId>sz-common-wechat</artifactId>
            <version>${revision}</version>
        </dependency>
        <!--日志模块-->
        <dependency>
            <groupId>com.sz</groupId>
            <artifactId>sz-common-log</artifactId>
            <version>${revision}</version>
        </dependency>
        <!--redis模块-->
        <dependency>
            <groupId>com.sz</groupId>
            <artifactId>sz-common-db-redis</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.sz.excel</groupId>
            <artifactId>sz-common-excel</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- oss模块 -->
        <dependency>
            <groupId>com.sz.oss</groupId>
            <artifactId>sz-common-oss</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- flyway db ddl; https://documentation.red-gate.com/fd/mysql-184127601.html-->
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
            <version>${flyway.version}</version>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-mysql</artifactId>
            <version>${flyway.version}</version>
        </dependency>
        <!-- aspect aop 切面-->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
            <version>${liquibase.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.50</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2-extension-spring6</artifactId>
            <version>2.0.50</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.27</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>33.3.0-jre</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.github.binarywang</groupId>-->
<!--            <artifactId>weixin-java-miniapp</artifactId>-->
<!--            <version>4.7.5-20250516.201941</version>-->
<!--        </dependency>-->
    </dependencies>
</project>