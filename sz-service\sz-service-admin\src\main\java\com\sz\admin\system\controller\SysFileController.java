package com.sz.admin.system.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson2.JSONObject;
import com.sz.admin.system.pojo.dto.sysfile.SysFileListDTO;
import com.sz.admin.system.pojo.po.SysFile;
import com.sz.admin.system.service.SysFileService;
import com.sz.core.common.annotation.DebounceIgnore;
import com.sz.core.common.entity.ApiPageResult;
import com.sz.core.common.entity.ApiResult;
import com.sz.core.common.entity.PageResult;
import com.sz.oss.UploadResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 公共文件 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-31
 */
@Tag(name = "系统公共文件管理")
@RequiredArgsConstructor
@RestController
@RequestMapping("/sys-file")
public class SysFileController {

    private final SysFileService sysFileService;

    // 文件类型到 MIME 类型的映射
    private static final Map<String, String> CONTENT_TYPE_MAP = new HashMap<>();

    static {
        CONTENT_TYPE_MAP.put("pdf", "application/pdf");
        CONTENT_TYPE_MAP.put("txt", "text/plain");
        CONTENT_TYPE_MAP.put("html", "text/html");
        CONTENT_TYPE_MAP.put("csv", "text/csv");
        CONTENT_TYPE_MAP.put("xml", "application/xml");
        CONTENT_TYPE_MAP.put("json", "application/json");
        // 添加其他你需要支持的文件类型
    }

    @Value("${fileRoot}")
    private String fileRoot;

    @Operation(summary = "列表查询")
    @GetMapping
    public ApiPageResult<PageResult<SysFile>> list(SysFileListDTO dto) {
        return ApiPageResult.success(sysFileService.fileList(dto));
    }

    @SaIgnore
    @Operation(summary = "上传文件")
    @PostMapping("/upload")
    public ApiResult<JSONObject> uploadByFormData(@RequestParam MultipartFile file) throws IOException {
        return ApiResult.success(sysFileService.uploadByFormData(file));
    }

    @SaIgnore
    @Operation(summary = "下载文件")
    @GetMapping("/getFile")
    public ResponseEntity<byte[]> getFile(@RequestParam String fileId, HttpServletResponse response) {

        String[] split = fileId.split(";");

        boolean preview = false;
        if (split.length > 1) {
            preview = Boolean.parseBoolean(split[1]);
        }


        SysFile file = sysFileService.getById(split[0]);
        if (file == null) {
            return ResponseEntity.notFound().build();
        }

        String fullFilePath = fileRoot + file.getFilePath() + "/" + file.getId() + "." + file.getFileSuffix();

        System.out.println("文件路径:" + fullFilePath);

        if (!FileUtil.exist(fullFilePath)) {
            // 文件不存在
            return ResponseEntity.notFound().build();
        }

        // 根据文件类型设置具体的 content type
        String fileType = file.getFileSuffix();
        String contentType = CONTENT_TYPE_MAP.getOrDefault(fileType, "application/octet-stream");

        MediaType mediaType = MediaType.APPLICATION_OCTET_STREAM;
        response.setHeader("Content-Disposition", "attachment; filename=\"" + new String(file.getFilename().getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1)  + "\"");
        if(preview){
            mediaType = MediaType.parseMediaType(contentType);
            response.setHeader("Content-Disposition", "inline; filename=\"" + new String(file.getFilename().getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1) + "\"");
        }


        return ResponseEntity.ok()
                .contentType(mediaType)
                .body(FileUtil.readBytes(fullFilePath));
    }

    /**
     * 根据ID数组查询文件列表
     *
     * @param jsonObject
     * @return {@link ApiResult }<{@link List }<{@link JSONObject }>>
     */
    @SaIgnore
    @Operation(summary = "根据ID数组查询文件列表")
    @PostMapping("/getByIds")
    public ApiResult<List<JSONObject>> getByIds(@RequestBody JSONObject jsonObject) {
        return ApiResult.success(sysFileService.getByIds(jsonObject.getString("ids")));
    }
}
