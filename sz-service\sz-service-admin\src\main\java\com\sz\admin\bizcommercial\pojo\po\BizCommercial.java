package com.sz.admin.bizcommercial.pojo.po;

import com.mybatisflex.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.io.Serial;
import com.sz.mysql.EntityChangeListener;
import java.time.LocalDateTime;

/**
* <p>
* 广告信息表
* </p>
*
* <AUTHOR>
* @since 2025-05-24
*/
@Data
@Table(value = "biz_commercial", onInsert = EntityChangeListener.class, onUpdate = EntityChangeListener.class)
@Schema(description = "广告信息表")
public class BizCommercial implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Schema(description ="主键唯一")
    private Long commercialId;

    @Schema(description ="文件ID")
    private Long fileId;

    @Schema(description ="创建人ID")
    private Long createId;

    @Schema(description ="创建时间")
    private LocalDateTime createTime;

    @Schema(description ="更新人ID")
    private Long updateId;

    @Schema(description ="更新时间")
    private LocalDateTime updateTime;

    @Schema(description ="显示顺序")
    private Integer sortNum;

    @Schema(description ="备注")
    private String remark;

    @Schema(description = "禁用/启用")
    private String isNot;
}