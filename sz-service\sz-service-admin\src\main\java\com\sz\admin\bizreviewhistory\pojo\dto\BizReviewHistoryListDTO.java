package com.sz.admin.bizreviewhistory.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.sz.core.common.entity.PageQuery;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * <p>
 * BizReviewHistory查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@Schema(description = "BizReviewHistory查询DTO")
public class BizReviewHistoryListDTO extends PageQuery {

    @Schema(description =  "审核状态")
    private String reviewStatus;

    @Schema(description =  "审核人")
    private Long auditor;

    @Schema(description =  "发布内容ID")
    private Long pubId;

    @Schema(description =  "创建时间")
    private LocalDateTime createTime;

    @Schema(description =  "审核内容")
    private String reviewContent;
}